{"openapi": "3.0.1", "info": {"title": "AirMonitor API", "description": "商用空调监控系统 API", "contact": {"name": "AirMonitor Team", "url": "https://www.airmonitor.com", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "version": "v1"}, "paths": {"/api/system/info": {"get": {"tags": ["System"], "operationId": "GetSystemInfo", "responses": {"200": {"description": "OK"}}}}, "/api/System/info": {"get": {"tags": ["System"], "responses": {"200": {"description": "OK"}}}}, "/api/System/config": {"get": {"tags": ["System"], "responses": {"200": {"description": "OK"}}}}, "/api/System/health": {"get": {"tags": ["System"], "responses": {"200": {"description": "OK"}}}}}, "components": {}}