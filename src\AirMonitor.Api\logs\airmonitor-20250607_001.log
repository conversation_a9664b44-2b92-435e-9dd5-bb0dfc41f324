[2025-06-07 23:06:19.212 +08:00 INF] Program: 正在启动 AirMonitor API 服务...
[2025-06-07 23:06:19.257 +08:00 INF] Program: AirMonitor API 服务启动成功，监听地址: http://localhost:5236
[2025-06-07 23:06:19.286 +08:00 INF] Microsoft.Hosting.Lifetime: Now listening on: http://localhost:5236
[2025-06-07 23:06:19.287 +08:00 INF] Microsoft.Hosting.Lifetime: Application started. Press Ctrl+C to shut down.
[2025-06-07 23:06:19.287 +08:00 INF] Microsoft.Hosting.Lifetime: Hosting environment: Development
[2025-06-07 23:06:19.288 +08:00 INF] Microsoft.Hosting.Lifetime: Content root path: D:\Project\08 AirMonitor\src\AirMonitor.Api
[2025-06-07 23:08:02.020 +08:00 INF] Program: 正在启动 AirMonitor API 服务...
[2025-06-07 23:08:02.068 +08:00 INF] Program: AirMonitor API 服务启动成功，监听地址: http://localhost:5236
[2025-06-07 23:08:02.099 +08:00 INF] Microsoft.Hosting.Lifetime: Now listening on: http://localhost:5236
[2025-06-07 23:08:02.099 +08:00 INF] Microsoft.Hosting.Lifetime: Application started. Press Ctrl+C to shut down.
[2025-06-07 23:08:02.100 +08:00 INF] Microsoft.Hosting.Lifetime: Hosting environment: Development
[2025-06-07 23:08:02.101 +08:00 INF] Microsoft.Hosting.Lifetime: Content root path: D:\Project\08 AirMonitor\src\AirMonitor.Api
[2025-06-07 23:08:51.550 +08:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET http://localhost:5236/ - null null
[2025-06-07 23:08:51.556 +08:00 INF] AirMonitor.Infrastructure.Middleware.RequestLoggingMiddleware: 开始处理请求 f872347c-4c94-4980-a686-ace64503fe17: GET / 
[2025-06-07 23:08:51.563 +08:00 INF] AirMonitor.Infrastructure.Middleware.RequestLoggingMiddleware: 完成处理请求 f872347c-4c94-4980-a686-ace64503fe17: GET / - 301 - 7ms
[2025-06-07 23:08:51.565 +08:00 INF] Serilog.AspNetCore.RequestLoggingMiddleware: HTTP GET / responded 301 in 10.6350 ms
[2025-06-07 23:08:51.569 +08:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET http://localhost:5236/ - 301 0 null 20.0197ms
[2025-06-07 23:08:51.573 +08:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET http://localhost:5236/index.html - null null
[2025-06-07 23:08:51.574 +08:00 INF] AirMonitor.Infrastructure.Middleware.RequestLoggingMiddleware: 开始处理请求 9f0ad350-4480-42c1-a124-b34773a0f378: GET /index.html 
[2025-06-07 23:08:51.608 +08:00 INF] AirMonitor.Infrastructure.Middleware.RequestLoggingMiddleware: 完成处理请求 9f0ad350-4480-42c1-a124-b34773a0f378: GET /index.html - 200 - 34ms
[2025-06-07 23:08:51.610 +08:00 INF] Serilog.AspNetCore.RequestLoggingMiddleware: HTTP GET /index.html responded 200 in 35.7697 ms
[2025-06-07 23:08:51.611 +08:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET http://localhost:5236/index.html - 200 null text/html;charset=utf-8 38.5534ms
[2025-06-07 23:08:51.614 +08:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET http://localhost:5236/swagger-ui.css - null null
[2025-06-07 23:08:51.614 +08:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET http://localhost:5236/swagger-ui-standalone-preset.js - null null
[2025-06-07 23:08:51.614 +08:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET http://localhost:5236/swagger-ui-bundle.js - null null
[2025-06-07 23:08:51.624 +08:00 INF] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
[2025-06-07 23:08:51.624 +08:00 INF] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
[2025-06-07 23:08:51.626 +08:00 INF] Serilog.AspNetCore.RequestLoggingMiddleware: HTTP GET /swagger-ui.css responded 200 in 9.2988 ms
[2025-06-07 23:08:51.626 +08:00 INF] Serilog.AspNetCore.RequestLoggingMiddleware: HTTP GET /swagger-ui-standalone-preset.js responded 200 in 8.5232 ms
[2025-06-07 23:08:51.627 +08:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET http://localhost:5236/swagger-ui.css - 200 152034 text/css 13.6188ms
[2025-06-07 23:08:51.628 +08:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET http://localhost:5236/swagger-ui-standalone-preset.js - 200 230280 text/javascript 14.2855ms
[2025-06-07 23:08:51.633 +08:00 INF] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
[2025-06-07 23:08:51.634 +08:00 INF] Serilog.AspNetCore.RequestLoggingMiddleware: HTTP GET /swagger-ui-bundle.js responded 200 in 15.3091 ms
[2025-06-07 23:08:51.635 +08:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET http://localhost:5236/swagger-ui-bundle.js - 200 1456926 text/javascript 21.5823ms
[2025-06-07 23:08:51.750 +08:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET http://localhost:5236/swagger/v1/swagger.json - null null
[2025-06-07 23:08:51.760 +08:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET http://localhost:5236/favicon-32x32.png - null null
[2025-06-07 23:08:51.761 +08:00 INF] AirMonitor.Infrastructure.Middleware.RequestLoggingMiddleware: 开始处理请求 3a343636-f901-4d28-a429-748238a60999: GET /favicon-32x32.png 
[2025-06-07 23:08:51.762 +08:00 INF] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
[2025-06-07 23:08:51.763 +08:00 INF] AirMonitor.Infrastructure.Middleware.RequestLoggingMiddleware: 完成处理请求 3a343636-f901-4d28-a429-748238a60999: GET /favicon-32x32.png - 200 - 1ms
[2025-06-07 23:08:51.764 +08:00 INF] Serilog.AspNetCore.RequestLoggingMiddleware: HTTP GET /favicon-32x32.png responded 200 in 3.4340 ms
[2025-06-07 23:08:51.766 +08:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET http://localhost:5236/favicon-32x32.png - 200 628 image/png 5.8484ms
[2025-06-07 23:08:51.773 +08:00 INF] Serilog.AspNetCore.RequestLoggingMiddleware: HTTP GET /swagger/v1/swagger.json responded 200 in 20.6932 ms
[2025-06-07 23:08:51.776 +08:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/1.1 GET http://localhost:5236/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 25.8568ms
2025-06-07 23:11:55.814 +08:00 [INF] 正在启动 AirMonitor API 服务...
2025-06-07 23:11:55.858 +08:00 [INF] AirMonitor API 服务启动成功，监听地址: http://localhost:5236
2025-06-07 23:11:55.887 +08:00 [INF] Now listening on: http://localhost:5236
2025-06-07 23:11:55.888 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-07 23:11:55.888 +08:00 [INF] Hosting environment: Development
2025-06-07 23:11:55.889 +08:00 [INF] Content root path: D:\Project\08 AirMonitor\src\AirMonitor.Api
2025-06-07 23:12:03.852 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/index.html - null null
2025-06-07 23:12:03.897 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/index.html - 200 null text/html;charset=utf-8 46.0219ms
2025-06-07 23:12:03.991 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/swagger/v1/swagger.json - null null
2025-06-07 23:12:04.012 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 20.5841ms
2025-06-07 23:12:18.316 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/api/system/info - null null
2025-06-07 23:12:18.319 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-07 23:12:18.327 +08:00 [ERR] 处理请求时发生未处理的异常: /api/system/info
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

HTTP: GET /api/system/info
AirMonitor.Api.Controllers.SystemController.GetSystemInfo (AirMonitor.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AirMonitor.Infrastructure.Middleware.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in D:\Project\08 AirMonitor\src\AirMonitor.Infrastructure\Middleware\ExceptionHandlingMiddleware.cs:line 31
2025-06-07 23:12:18.352 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/api/system/info - 500 null application/json 36.4588ms
2025-06-07 23:12:42.691 +08:00 [INF] 正在启动 AirMonitor API 服务...
2025-06-07 23:12:42.764 +08:00 [INF] AirMonitor API 服务启动成功，监听地址: https://localhost:7260, http://localhost:5236
2025-06-07 23:12:42.877 +08:00 [INF] Now listening on: https://localhost:7260
2025-06-07 23:12:42.878 +08:00 [INF] Now listening on: http://localhost:5236
2025-06-07 23:12:42.919 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-07 23:12:42.919 +08:00 [INF] Hosting environment: Development
2025-06-07 23:12:42.920 +08:00 [INF] Content root path: D:\Project\08 AirMonitor\src\AirMonitor.Api
2025-06-07 23:12:43.483 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7260/swagger - null null
2025-06-07 23:12:43.603 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7260/swagger - 404 0 null 122.8933ms
2025-06-07 23:12:43.606 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET https://localhost:7260/swagger, Response status code: 404
2025-06-07 23:13:34.715 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/index.html - null null
2025-06-07 23:13:34.757 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/index.html - 200 null text/html;charset=utf-8 41.7922ms
2025-06-07 23:13:34.759 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/_framework/aspnetcore-browser-refresh.js - null null
2025-06-07 23:13:34.761 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/_vs/browserLink - null null
2025-06-07 23:13:34.763 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/_framework/aspnetcore-browser-refresh.js - 200 16513 application/javascript; charset=utf-8 4.5104ms
2025-06-07 23:13:34.790 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/_vs/browserLink - 200 null text/javascript; charset=UTF-8 29.03ms
2025-06-07 23:13:34.811 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/swagger/v1/swagger.json - null null
2025-06-07 23:13:34.819 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 8.3246ms
2025-06-07 23:13:41.497 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5236/api/system/info - null null
2025-06-07 23:13:41.500 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5236/api/system/info - 307 0 null 3.8539ms
2025-06-07 23:13:41.507 +08:00 [INF] Request starting HTTP/2 GET https://localhost:7260/api/system/info - null null
2025-06-07 23:13:41.511 +08:00 [INF] CORS policy execution successful.
2025-06-07 23:13:41.533 +08:00 [ERR] 处理请求时发生未处理的异常: /api/system/info
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

HTTP: GET /api/system/info
AirMonitor.Api.Controllers.SystemController.GetSystemInfo (AirMonitor.Api)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at AirMonitor.Infrastructure.Middleware.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in D:\Project\08 AirMonitor\src\AirMonitor.Infrastructure\Middleware\ExceptionHandlingMiddleware.cs:line 31
2025-06-07 23:13:41.554 +08:00 [INF] Request finished HTTP/2 GET https://localhost:7260/api/system/info - 500 null application/json 47.5893ms
