[{"ContainingType": "AirMonitor.Api.Controllers.SystemController", "Method": "GetConfiguration", "RelativePath": "api/System/config", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [], "Tags": ["System"]}, {"ContainingType": "AirMonitor.Api.Controllers.SystemController", "Method": "HealthCheck", "RelativePath": "api/System/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [], "Tags": ["System"]}, {"ContainingType": "Program+<>c__DisplayClass0_0", "Method": "<<Main>$>b__3", "RelativePath": "api/system/info", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}], "Tags": ["System"], "EndpointName": "GetSystemInfo"}, {"ContainingType": "AirMonitor.Api.Controllers.SystemController", "Method": "GetSystemInfo", "RelativePath": "api/System/info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [], "Tags": ["System"]}]