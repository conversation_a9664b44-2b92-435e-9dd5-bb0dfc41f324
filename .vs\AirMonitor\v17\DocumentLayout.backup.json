{"Version": 1, "WorkspaceRootPath": "D:\\Project\\08 AirMonitor\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{7FB0589C-76E0-4DB8-A856-AF7F7EEF9438}|src\\AirMonitor.Infrastructure\\AirMonitor.Infrastructure.csproj|d:\\project\\08 airmonitor\\src\\airmonitor.infrastructure\\configuration\\apisettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7FB0589C-76E0-4DB8-A856-AF7F7EEF9438}|src\\AirMonitor.Infrastructure\\AirMonitor.Infrastructure.csproj|solutionrelative:src\\airmonitor.infrastructure\\configuration\\apisettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7FB0589C-76E0-4DB8-A856-AF7F7EEF9438}|src\\AirMonitor.Infrastructure\\AirMonitor.Infrastructure.csproj|d:\\project\\08 airmonitor\\src\\airmonitor.infrastructure\\configuration\\databasesettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7FB0589C-76E0-4DB8-A856-AF7F7EEF9438}|src\\AirMonitor.Infrastructure\\AirMonitor.Infrastructure.csproj|solutionrelative:src\\airmonitor.infrastructure\\configuration\\databasesettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7FB0589C-76E0-4DB8-A856-AF7F7EEF9438}|src\\AirMonitor.Infrastructure\\AirMonitor.Infrastructure.csproj|d:\\project\\08 airmonitor\\src\\airmonitor.infrastructure\\configuration\\appsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7FB0589C-76E0-4DB8-A856-AF7F7EEF9438}|src\\AirMonitor.Infrastructure\\AirMonitor.Infrastructure.csproj|solutionrelative:src\\airmonitor.infrastructure\\configuration\\appsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "ApiSettings.cs", "DocumentMoniker": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Infrastructure\\Configuration\\ApiSettings.cs", "RelativeDocumentMoniker": "src\\AirMonitor.Infrastructure\\Configuration\\ApiSettings.cs", "ToolTip": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Infrastructure\\Configuration\\ApiSettings.cs", "RelativeToolTip": "src\\AirMonitor.Infrastructure\\Configuration\\ApiSettings.cs", "ViewState": "AgIAAHsAAAAAAAAAAAAAwIQAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-07T14:45:35.162Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "DatabaseSettings.cs", "DocumentMoniker": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Infrastructure\\Configuration\\DatabaseSettings.cs", "RelativeDocumentMoniker": "src\\AirMonitor.Infrastructure\\Configuration\\DatabaseSettings.cs", "ToolTip": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Infrastructure\\Configuration\\DatabaseSettings.cs", "RelativeToolTip": "src\\AirMonitor.Infrastructure\\Configuration\\DatabaseSettings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-07T14:45:33.717Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "AppSettings.cs", "DocumentMoniker": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Infrastructure\\Configuration\\AppSettings.cs", "RelativeDocumentMoniker": "src\\AirMonitor.Infrastructure\\Configuration\\AppSettings.cs", "ToolTip": "D:\\Project\\08 AirMonitor\\src\\AirMonitor.Infrastructure\\Configuration\\AppSettings.cs", "RelativeToolTip": "src\\AirMonitor.Infrastructure\\Configuration\\AppSettings.cs", "ViewState": "AgIAADsAAAAAAAAAAAAkwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-07T14:45:24.7Z", "EditorCaption": ""}]}]}]}