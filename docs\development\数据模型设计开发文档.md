# 数据模型设计 开发文档

## 1. 功能需求描述

### 1.1 业务背景
数据模型是 AirMonitor 商用空调监控系统的核心基础，需要支持多设备、多用户、多租户的空调设备监控场景。系统需要存储设备信息、实时监控数据、历史数据、用户信息、配置参数等多种类型的数据。

### 1.2 功能范围
- **设备管理**：空调设备基本信息、设备分组、设备状态
- **数据采集**：实时监控数据、历史数据存储、数据聚合
- **用户管理**：用户账户、角色权限、多租户支持
- **配置管理**：设备配置参数、系统配置、告警规则
- **日志审计**：操作日志、系统日志、数据变更记录
- **报表统计**：数据统计、报表生成、数据分析

### 1.3 用户故事
- 作为系统管理员，我希望能够管理多个租户的设备和用户
- 作为设备管理员，我希望能够添加、配置和监控空调设备
- 作为运维人员，我希望能够查看设备的实时状态和历史数据
- 作为数据分析师，我希望能够获取设备的统计数据和趋势分析

## 2. 技术实现方案

### 2.1 架构设计
```
数据访问层 (Data Access Layer)
├── DbContext/                    # Entity Framework 上下文
│   ├── AirMonitorDbContext.cs   # 主数据库上下文
│   └── AuditDbContext.cs        # 审计数据库上下文
├── Entities/                    # 实体模型
│   ├── Device/                  # 设备相关实体
│   ├── Monitoring/              # 监控数据实体
│   ├── User/                    # 用户管理实体
│   ├── Configuration/           # 配置管理实体
│   └── Audit/                   # 审计日志实体
├── Configurations/              # 实体配置
│   ├── DeviceConfiguration.cs  # 设备实体配置
│   └── MonitoringConfiguration.cs # 监控数据配置
└── Migrations/                  # 数据库迁移
    └── Initial/                 # 初始迁移文件
```

### 2.2 技术选型
- **ORM 框架**：Entity Framework Core 8.0
- **数据库**：SQL Server 2022 (主库) + Redis (缓存)
- **数据库设计**：Code First 模式
- **数据迁移**：EF Core Migrations
- **数据验证**：Data Annotations + Fluent Validation
- **审计日志**：EF Core Interceptors + 审计实体

### 2.3 设计模式
- **仓储模式 (Repository Pattern)**：数据访问抽象
- **工作单元模式 (Unit of Work)**：事务管理
- **领域驱动设计 (DDD)**：实体聚合根设计
- **CQRS 模式**：读写分离（查询优化）

## 3. 核心实体设计

### 3.1 设备管理实体

#### 3.1.1 设备实体 (Device)
```csharp
public class Device : BaseEntity
{
    public string DeviceId { get; set; }           // 设备唯一标识
    public string DeviceName { get; set; }         // 设备名称
    public string DeviceType { get; set; }         // 设备类型
    public string Model { get; set; }              // 设备型号
    public string Manufacturer { get; set; }       // 制造商
    public string SerialNumber { get; set; }       // 序列号
    public string Location { get; set; }           // 安装位置
    public DeviceStatus Status { get; set; }       // 设备状态
    public DateTime InstallDate { get; set; }      // 安装日期
    public DateTime? LastMaintenanceDate { get; set; } // 最后维护日期
    
    // 通信配置
    public string SerialPortName { get; set; }     // 串口名称
    public int BaudRate { get; set; }              // 波特率
    public string ProtocolType { get; set; }       // 通信协议类型
    
    // 关联关系
    public int TenantId { get; set; }              // 租户ID
    public Tenant Tenant { get; set; }             // 租户
    public int? DeviceGroupId { get; set; }        // 设备组ID
    public DeviceGroup DeviceGroup { get; set; }   // 设备组
    
    // 导航属性
    public ICollection<MonitoringData> MonitoringData { get; set; }
    public ICollection<DeviceConfiguration> Configurations { get; set; }
    public ICollection<AlarmRecord> AlarmRecords { get; set; }
}

public enum DeviceStatus
{
    Online = 1,      // 在线
    Offline = 2,     // 离线
    Fault = 3,       // 故障
    Maintenance = 4  // 维护中
}
```

#### 3.1.2 设备组实体 (DeviceGroup)
```csharp
public class DeviceGroup : BaseEntity
{
    public string GroupName { get; set; }          // 组名称
    public string Description { get; set; }        // 描述
    public int TenantId { get; set; }              // 租户ID
    public Tenant Tenant { get; set; }             // 租户
    
    // 导航属性
    public ICollection<Device> Devices { get; set; }
}
```

### 3.2 监控数据实体

#### 3.2.1 实时监控数据 (MonitoringData)
```csharp
public class MonitoringData : BaseEntity
{
    public int DeviceId { get; set; }              // 设备ID
    public Device Device { get; set; }             // 设备
    
    public DateTime Timestamp { get; set; }        // 时间戳
    public DataType DataType { get; set; }         // 数据类型
    
    // 温度数据
    public decimal? IndoorTemperature { get; set; }    // 室内温度
    public decimal? OutdoorTemperature { get; set; }   // 室外温度
    public decimal? SetTemperature { get; set; }       // 设定温度
    
    // 湿度数据
    public decimal? IndoorHumidity { get; set; }       // 室内湿度
    public decimal? OutdoorHumidity { get; set; }      // 室外湿度
    
    // 运行状态
    public bool IsRunning { get; set; }                // 是否运行
    public AirConditionerMode Mode { get; set; }       // 运行模式
    public int FanSpeed { get; set; }                  // 风速等级
    
    // 能耗数据
    public decimal? PowerConsumption { get; set; }     // 功耗 (kW)
    public decimal? Voltage { get; set; }              // 电压 (V)
    public decimal? Current { get; set; }              // 电流 (A)
    
    // 压力数据
    public decimal? HighPressure { get; set; }         // 高压 (MPa)
    public decimal? LowPressure { get; set; }          // 低压 (MPa)
    
    // 故障代码
    public string FaultCode { get; set; }              // 故障代码
    public string FaultDescription { get; set; }       // 故障描述
}

public enum DataType
{
    RealTime = 1,    // 实时数据
    Historical = 2,  // 历史数据
    Alarm = 3        // 告警数据
}

public enum AirConditionerMode
{
    Auto = 1,        // 自动模式
    Cool = 2,        // 制冷模式
    Heat = 3,        // 制热模式
    Fan = 4,         // 送风模式
    Dry = 5          // 除湿模式
}
```

#### 3.2.2 历史数据聚合 (HistoricalDataSummary)
```csharp
public class HistoricalDataSummary : BaseEntity
{
    public int DeviceId { get; set; }              // 设备ID
    public Device Device { get; set; }             // 设备
    
    public DateTime Date { get; set; }             // 日期
    public SummaryType SummaryType { get; set; }   // 聚合类型
    
    // 温度统计
    public decimal? AvgIndoorTemperature { get; set; }  // 平均室内温度
    public decimal? MaxIndoorTemperature { get; set; }  // 最高室内温度
    public decimal? MinIndoorTemperature { get; set; }  // 最低室内温度
    
    // 能耗统计
    public decimal? TotalPowerConsumption { get; set; } // 总功耗
    public decimal? AvgPowerConsumption { get; set; }   // 平均功耗
    public decimal? MaxPowerConsumption { get; set; }   // 最大功耗
    
    // 运行时间统计
    public int TotalRunningMinutes { get; set; }        // 总运行时间(分钟)
    public int CoolingMinutes { get; set; }             // 制冷时间
    public int HeatingMinutes { get; set; }             // 制热时间
    
    // 故障统计
    public int FaultCount { get; set; }                 // 故障次数
    public int AlarmCount { get; set; }                 // 告警次数
}

public enum SummaryType
{
    Hourly = 1,      // 小时汇总
    Daily = 2,       // 日汇总
    Monthly = 3,     // 月汇总
    Yearly = 4       // 年汇总
}
```

### 3.3 用户管理实体

#### 3.3.1 租户实体 (Tenant)
```csharp
public class Tenant : BaseEntity
{
    public string TenantName { get; set; }         // 租户名称
    public string TenantCode { get; set; }         // 租户代码
    public string ContactPerson { get; set; }      // 联系人
    public string ContactPhone { get; set; }       // 联系电话
    public string ContactEmail { get; set; }       // 联系邮箱
    public string Address { get; set; }            // 地址
    public bool IsActive { get; set; }             // 是否激活
    public DateTime? ExpiryDate { get; set; }      // 到期日期
    
    // 配额限制
    public int MaxDeviceCount { get; set; }        // 最大设备数
    public int MaxUserCount { get; set; }          // 最大用户数
    public long MaxDataStorageMB { get; set; }     // 最大数据存储(MB)
    
    // 导航属性
    public ICollection<User> Users { get; set; }
    public ICollection<Device> Devices { get; set; }
    public ICollection<DeviceGroup> DeviceGroups { get; set; }
}
```

#### 3.3.2 用户实体 (User)
```csharp
public class User : BaseEntity
{
    public string Username { get; set; }           // 用户名
    public string Email { get; set; }              // 邮箱
    public string PasswordHash { get; set; }       // 密码哈希
    public string FirstName { get; set; }          // 名
    public string LastName { get; set; }           // 姓
    public string Phone { get; set; }              // 电话
    public bool IsActive { get; set; }             // 是否激活
    public DateTime? LastLoginTime { get; set; }   // 最后登录时间
    public DateTime? PasswordChangedTime { get; set; } // 密码修改时间
    
    public int TenantId { get; set; }              // 租户ID
    public Tenant Tenant { get; set; }             // 租户
    
    // 导航属性
    public ICollection<UserRole> UserRoles { get; set; }
    public ICollection<UserDevicePermission> DevicePermissions { get; set; }
}
```

### 3.4 基础实体类

#### 3.4.1 基础实体 (BaseEntity)
```csharp
public abstract class BaseEntity
{
    public int Id { get; set; }                    // 主键
    public DateTime CreatedTime { get; set; }      // 创建时间
    public DateTime? UpdatedTime { get; set; }     // 更新时间
    public string CreatedBy { get; set; }          // 创建人
    public string UpdatedBy { get; set; }          // 更新人
    public bool IsDeleted { get; set; }            // 软删除标记
    public DateTime? DeletedTime { get; set; }     // 删除时间
    public string DeletedBy { get; set; }          // 删除人
    public byte[] RowVersion { get; set; }         // 并发控制
}
```

## 4. 数据库设计

### 4.1 表结构设计
- **设备管理表**：Devices, DeviceGroups, DeviceConfigurations
- **监控数据表**：MonitoringData, HistoricalDataSummary
- **用户管理表**：Tenants, Users, Roles, UserRoles
- **配置管理表**：SystemConfigurations, AlarmRules
- **审计日志表**：AuditLogs, OperationLogs

### 4.2 索引设计
- **设备查询索引**：DeviceId, TenantId, Status
- **监控数据索引**：DeviceId + Timestamp, DataType
- **用户查询索引**：Username, Email, TenantId
- **时间序列索引**：Timestamp (分区索引)

### 4.3 分区策略
- **监控数据分区**：按月分区，提高查询性能
- **历史数据分区**：按年分区，便于数据归档
- **审计日志分区**：按季度分区，控制表大小

## 5. Entity Framework 配置

### 5.1 DbContext 配置
```csharp
public class AirMonitorDbContext : DbContext
{
    public DbSet<Device> Devices { get; set; }
    public DbSet<MonitoringData> MonitoringData { get; set; }
    public DbSet<Tenant> Tenants { get; set; }
    public DbSet<User> Users { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // 应用所有实体配置
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(AirMonitorDbContext).Assembly);
        
        // 全局查询过滤器（软删除）
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            if (typeof(BaseEntity).IsAssignableFrom(entityType.ClrType))
            {
                modelBuilder.Entity(entityType.ClrType)
                    .HasQueryFilter(GetSoftDeleteFilter(entityType.ClrType));
            }
        }
    }
}
```

### 5.2 实体配置示例
```csharp
public class DeviceConfiguration : IEntityTypeConfiguration<Device>
{
    public void Configure(EntityTypeBuilder<Device> builder)
    {
        builder.HasKey(e => e.Id);
        
        builder.Property(e => e.DeviceId)
            .IsRequired()
            .HasMaxLength(50)
            .HasComment("设备唯一标识");
            
        builder.Property(e => e.DeviceName)
            .IsRequired()
            .HasMaxLength(100)
            .HasComment("设备名称");
            
        builder.HasIndex(e => e.DeviceId)
            .IsUnique()
            .HasDatabaseName("IX_Device_DeviceId");
            
        builder.HasOne(e => e.Tenant)
            .WithMany(t => t.Devices)
            .HasForeignKey(e => e.TenantId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
```

## 6. 数据访问层设计

### 6.1 仓储接口
```csharp
public interface IDeviceRepository : IRepository<Device>
{
    Task<Device> GetByDeviceIdAsync(string deviceId);
    Task<IEnumerable<Device>> GetByTenantIdAsync(int tenantId);
    Task<IEnumerable<Device>> GetOnlineDevicesAsync();
    Task<bool> IsDeviceIdExistsAsync(string deviceId);
}
```

### 6.2 工作单元接口
```csharp
public interface IUnitOfWork : IDisposable
{
    IDeviceRepository Devices { get; }
    IMonitoringDataRepository MonitoringData { get; }
    IUserRepository Users { get; }
    ITenantRepository Tenants { get; }
    
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    Task BeginTransactionAsync();
    Task CommitTransactionAsync();
    Task RollbackTransactionAsync();
}
```

## 7. 数据验证与约束

### 7.1 数据注解验证
```csharp
[Required(ErrorMessage = "设备ID不能为空")]
[StringLength(50, ErrorMessage = "设备ID长度不能超过50个字符")]
public string DeviceId { get; set; }

[Range(0, 100, ErrorMessage = "温度值必须在0-100之间")]
public decimal? Temperature { get; set; }
```

### 7.2 Fluent Validation
```csharp
public class DeviceValidator : AbstractValidator<Device>
{
    public DeviceValidator()
    {
        RuleFor(x => x.DeviceId)
            .NotEmpty().WithMessage("设备ID不能为空")
            .Length(1, 50).WithMessage("设备ID长度必须在1-50个字符之间")
            .Matches(@"^[A-Z0-9_-]+$").WithMessage("设备ID只能包含大写字母、数字、下划线和连字符");
            
        RuleFor(x => x.SerialPortName)
            .NotEmpty().When(x => x.Status == DeviceStatus.Online)
            .WithMessage("在线设备必须指定串口名称");
    }
}
```

## 8. 性能优化策略

### 8.1 查询优化
- **分页查询**：避免大数据量查询
- **投影查询**：只查询需要的字段
- **预加载**：合理使用 Include 避免 N+1 查询
- **异步查询**：使用 async/await 提高并发性能

### 8.2 缓存策略
- **实体缓存**：缓存设备基本信息
- **查询缓存**：缓存常用查询结果
- **分布式缓存**：使用 Redis 缓存热点数据

### 8.3 数据归档
- **历史数据归档**：定期归档旧的监控数据
- **日志清理**：定期清理过期的审计日志
- **数据压缩**：对归档数据进行压缩存储

## 9. 安全考虑

### 9.1 数据安全
- **敏感数据加密**：密码、通信密钥等敏感信息加密存储
- **数据脱敏**：日志中避免记录敏感信息
- **访问控制**：基于租户和角色的数据访问控制

### 9.2 并发控制
- **乐观锁**：使用 RowVersion 防止并发更新冲突
- **悲观锁**：关键操作使用数据库锁
- **分布式锁**：跨服务的并发控制

## 10. 监控与审计

### 10.1 数据变更审计
- **审计拦截器**：自动记录数据变更
- **变更日志**：记录谁在什么时间修改了什么数据
- **操作追踪**：记录用户的关键操作

### 10.2 性能监控
- **查询性能监控**：监控慢查询
- **连接池监控**：监控数据库连接使用情况
- **存储空间监控**：监控数据库存储空间使用

## 11. 数据迁移策略

### 11.1 初始迁移
```csharp
// 创建初始迁移
dotnet ef migrations add InitialCreate --project AirMonitor.Infrastructure

// 应用迁移到数据库
dotnet ef database update --project AirMonitor.Infrastructure
```

### 11.2 版本升级迁移
- **向后兼容**：新版本数据库结构向后兼容
- **数据迁移脚本**：提供数据迁移和转换脚本
- **回滚策略**：提供数据库回滚方案

### 11.3 生产环境迁移
- **备份策略**：迁移前完整备份数据库
- **分步迁移**：大表分批迁移，减少停机时间
- **验证机制**：迁移后数据完整性验证

## 12. 测试策略

### 12.1 单元测试
```csharp
[Test]
public async Task CreateDevice_ValidDevice_ShouldReturnSuccess()
{
    // Arrange
    var device = new Device
    {
        DeviceId = "AC001",
        DeviceName = "办公室空调",
        TenantId = 1
    };

    // Act
    var result = await _deviceRepository.AddAsync(device);

    // Assert
    Assert.IsNotNull(result);
    Assert.AreEqual("AC001", result.DeviceId);
}
```

### 12.2 集成测试
- **数据库集成测试**：使用内存数据库测试
- **仓储层测试**：测试数据访问逻辑
- **事务测试**：测试事务回滚和提交

### 12.3 性能测试
- **大数据量测试**：测试百万级数据查询性能
- **并发测试**：测试多用户并发访问
- **压力测试**：测试系统极限负载

## 13. 部署配置

### 13.1 连接字符串配置
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.;Database=AirMonitor;Trusted_Connection=true;TrustServerCertificate=true;",
    "ReadOnlyConnection": "Server=.;Database=AirMonitor_ReadOnly;Trusted_Connection=true;TrustServerCertificate=true;",
    "Redis": "localhost:6379"
  }
}
```

### 13.2 数据库配置
```json
{
  "Database": {
    "CommandTimeout": 30,
    "MaxPoolSize": 100,
    "MinPoolSize": 5,
    "EnableSensitiveDataLogging": false,
    "EnableDetailedErrors": false,
    "EnableAutoMigration": false,
    "BatchSize": 1000,
    "QueryTrackingBehavior": "TrackAll",
    "EnableSplitQueries": true,
    "RetryCount": 3,
    "RetryDelay": 1000
  }
}
```

## 14. 开发检查清单

### 14.1 实体设计检查
- [ ] 所有实体继承自 BaseEntity
- [ ] 主键和外键正确定义
- [ ] 导航属性正确配置
- [ ] 数据注解和验证规则完整
- [ ] 索引设计合理

### 14.2 数据库设计检查
- [ ] 表名和字段名符合命名规范
- [ ] 外键约束正确设置
- [ ] 索引覆盖常用查询
- [ ] 分区策略合理
- [ ] 备份和恢复策略制定

### 14.3 代码质量检查
- [ ] 仓储接口和实现完整
- [ ] 工作单元模式正确实现
- [ ] 异常处理机制完善
- [ ] 日志记录充分
- [ ] 单元测试覆盖率 ≥ 80%

## 15. 风险评估与缓解

### 15.1 技术风险
- **数据库性能风险**：大数据量查询可能导致性能问题
  - 缓解措施：分页查询、索引优化、读写分离
- **并发冲突风险**：多用户同时操作可能导致数据冲突
  - 缓解措施：乐观锁、事务隔离、重试机制

### 15.2 业务风险
- **数据丢失风险**：硬件故障或操作失误可能导致数据丢失
  - 缓解措施：定期备份、主从复制、软删除机制
- **数据安全风险**：敏感数据泄露风险
  - 缓解措施：数据加密、访问控制、审计日志

## 16. 后续优化方向

### 16.1 性能优化
- **读写分离**：实现主从数据库读写分离
- **分库分表**：按租户或时间维度分库分表
- **缓存优化**：多级缓存策略优化

### 16.2 功能扩展
- **时序数据库**：考虑使用 InfluxDB 存储时序数据
- **数据湖**：构建数据湖支持大数据分析
- **实时计算**：集成流计算引擎处理实时数据

### 16.3 运维优化
- **自动化运维**：数据库自动化部署和监控
- **智能告警**：基于机器学习的异常检测
- **容灾备份**：多地域容灾备份方案
