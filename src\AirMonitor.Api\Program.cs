using AirMonitor.Infrastructure.DependencyInjection;
using AirMonitor.Infrastructure.Middleware;
using AirMonitor.Infrastructure.HealthChecks;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// 配置 Serilog（简化版本）
try
{
    builder.Host.UseSerilog((context, configuration) =>
    {
        configuration
            .MinimumLevel.Information()
            .Enrich.FromLogContext()
            .WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}")
            .WriteTo.File("logs/airmonitor-.log",
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 7);
    });
}
catch (Exception ex)
{
    Console.WriteLine($"Serilog 配置失败: {ex.Message}");
    // 继续使用默认日志配置
}

// 添加基础架构服务
builder.Services.AddAirMonitorInfrastructure(builder.Configuration);

// 添加控制器
builder.Services.AddControllers();

// 添加 API 探索和 Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "AirMonitor API",
        Version = "v1",
        Description = "商用空调监控系统 API",
        Contact = new Microsoft.OpenApi.Models.OpenApiContact
        {
            Name = "AirMonitor Team",
            Email = "<EMAIL>",
            Url = new Uri("https://www.airmonitor.com")
        },
        License = new Microsoft.OpenApi.Models.OpenApiLicense
        {
            Name = "MIT",
            Url = new Uri("https://opensource.org/licenses/MIT")
        }
    });
});

// 添加自定义健康检查
builder.Services.AddHealthChecks()
    .AddCheck<SerialPortHealthCheck>("serialport", tags: new[] { "hardware", "serialport" })
    .AddCheck<ApplicationHealthCheck>("application", tags: new[] { "application", "system" });

var app = builder.Build();

// 配置请求管道
var logger = app.Services.GetRequiredService<ILogger<Program>>();
logger.LogInformation("正在启动 AirMonitor API 服务...");

// 全局异常处理中间件
app.UseMiddleware<ExceptionHandlingMiddleware>();

// 简化的请求日志记录（暂时禁用详细的请求日志中间件）
// app.UseMiddleware<RequestLoggingMiddleware>();

// 开发环境配置
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "AirMonitor API v1");
        c.RoutePrefix = string.Empty; // 设置 Swagger UI 为根路径
    });
}

// HTTPS 重定向
app.UseHttpsRedirection();

// CORS
app.UseCors();

// 路由
app.UseRouting();

// 健康检查端点
app.MapHealthChecks("/health", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
{
    ResponseWriter = async (context, report) =>
    {
        context.Response.ContentType = "application/json";
        var response = new
        {
            status = report.Status.ToString(),
            checks = report.Entries.Select(x => new
            {
                name = x.Key,
                status = x.Value.Status.ToString(),
                description = x.Value.Description,
                data = x.Value.Data,
                duration = x.Value.Duration.TotalMilliseconds
            }),
            totalDuration = report.TotalDuration.TotalMilliseconds
        };
        await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
    }
});

// 控制器路由
app.MapControllers();

// 示例 API 端点
app.MapGet("/api/system/info", () =>
{
    return Results.Ok(new
    {
        applicationName = "AirMonitor",
        version = "1.0.0",
        environment = app.Environment.EnvironmentName,
        timestamp = DateTime.UtcNow,
        status = "运行中"
    });
})
.WithName("GetSystemInfo")
.WithTags("System")
.WithOpenApi();

// 启动应用程序
try
{
    logger.LogInformation("AirMonitor API 服务启动成功，监听地址: {Urls}",
        string.Join(", ", builder.WebHost.GetSetting("urls")?.Split(';') ?? new[] { "未知" }));

    app.Run();
}
catch (Exception ex)
{
    logger.LogCritical(ex, "AirMonitor API 服务启动失败");
    throw;
}
finally
{
    Log.CloseAndFlush();
}
